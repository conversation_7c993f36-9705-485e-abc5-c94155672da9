{"name": "ai-trainer-v1-root", "private": true, "scripts": {"dev": "concurrently \"npm --prefix backend run dev\" \"npm --prefix frontend run dev\"", "dev:unified": "npm run build:backend && npm --prefix frontend run dev:custom", "install:all": "npm --prefix backend install && npm --prefix frontend install", "build": "npm run clean:dist && npm run build:backend && npm run build:frontend && npm run create:unified && npm run install:dist", "build:static": "npm run clean:dist && npm run build:backend && npm run build:frontend:static && npm run create:static", "build:quick": "npm run clean:dist && npm run build:backend && npm run build:frontend && npm run create:unified", "install:dist": "cd dist && npm install --production", "build:backend": "npm --prefix backend run build", "build:frontend": "npm --prefix frontend run build", "build:frontend:static": "npm --prefix frontend run build", "create:unified": "node scripts/create-unified-build.js", "create:static": "node scripts/create-static-build.js", "clean:dist": "<PERSON><PERSON><PERSON> dist", "copy:assets": "node scripts/copy-assets.js", "start": "node dist/server.js", "start:dev": "NODE_ENV=development node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "test:build": "npm run build && npm run start:dev", "test:static": "npm run build:static && echo 'Static build ready in dist/static/'", "preview": "npm run build && echo 'Starting preview server...' && npm run start:dev", "postbuild": "echo '✅ Unified build created and dependencies installed!'", "prebuild": "echo '🚀 Starting unified build process...'", "debug:backend": "cd backend && dir && npm run build && dir dist", "debug:structure": "echo Root: && dir && echo Backend: && dir backend && echo Frontend: && dir frontend", "debug:backend-files": "echo Backend files: && dir backend\\*.js && dir backend\\*.json"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^6.0.1"}, "dependencies": {"@fontsource/poppins": "^5.2.6", "@next/font": "^14.2.15", "express": "^4.18.3", "next": "15.3.3"}}